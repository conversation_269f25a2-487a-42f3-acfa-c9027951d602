# To-Do List (Suivi de Projet)

---

## ✅ Fait

- Naviguer dans la scène  
- Affichage d'un objet avec une couleur simple  
- Charger des objets au format OBJ en utilisant la bibliothèque TinyOBJLoader,  
  en gérant les matériaux (couleurs ambiantes, diffuses, spéculaires)  
- Avoir de l’illumination avec l’équation de Phong ou Blinn–Phong
- Affichage des touches de navigation  
- Intégrer une interface graphique avec IMGUI (https://github.com/ocornut/imgui)
- Affichage des touches de navigation  

---

## 🟡 Fait ? / À voir ?

- Choix entre des objets avec shading Lambert, Phong, etc.  
- Utiliser un UBO pour la projection + caméra et un autre pour la transformation  
- Implémenter une classe `mat4` en C++ incluant la multiplication de matrices
---

## ❌ Non fait

- Ajout d'autres objets (ex. : une étoile, un autre vaisseau qui tire, un objet avec environment map, etc.) Une sphere (lune) ajouter avec une texture et rotation 
- Implémenter les FBO et ajouter du post-traitement  

