dans le dossiers ./models
il y a tjrs un fichier .obj avec son .mtl

Dans le main.cpp on va charger le modèle 3D et l'afficher à l'écran.

- On va utiliser la bibliothèque TinyOBJLoader pour charger le modèle et ses textures.
- On va aussi utiliser GLFW pour créer une fenêtre et gérer les entrées utilisateur.

Pour lancer le programme, il faut d'abord compiler le code.

Avec MINGW64 :

```bash

pacman -S mingw-w64-x86_64-toolchain \
           mingw-w64-x86_64-glew \
           mingw-w64-x86_64-glfw \
           mingw-w64-x86_64-glm \
           mingw-w64-x86_64-openal \
           mingw-w64-x86_64-cmake

cd ..
rm -rf build        # supprime l’ancienne build (cache CMake, binaires…)
mkdir build && cd build
cmake -G "Unix Makefiles" ..
make
```

<PERSON><PERSON><PERSON> exécuter le binaire :

```bash
./ProjetOpenGL.exe
```

## Système Audio

Le projet inclut maintenant un système de gestion audio 3D avec les fonctionnalités suivantes :

### Contrôles clavier :
- **ESPACE** : Jouer/Arrêter le son d'ambiance (Zoo.wav)
- **P** : Pause/Reprendre la lecture
- **O** : Arrêter tous les sons

### Interface graphique :
- Panneau "Contrôles Audio" avec sliders pour le volume
- Contrôles de lecture (Play/Pause/Stop)
- Réglage du pitch (hauteur du son)

### Fichiers audio supportés :
- **WAV** : Support complet (PCM 8/16 bits, mono/stéréo)

### Architecture audio :
- **SoundManager** : Gestionnaire principal du système audio OpenAL
- **Sound** : Représente un fichier audio chargé en mémoire
- **AudioSource** : Source audio 3D positionnée dans l'espace avec atténuation

Le son `Zoo.wav` dans le dossier `sound/` sera automatiquement chargé au démarrage.
L'audio 3D suit la position de la caméra pour un rendu spatial réaliste.
